<?php
// Include database connection
include '../../includes/session.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include permission functions
include_once '../../includes/functions/permission_functions.php';

// Check if user has permission to access bail module
if (!canAccessModule('bail')) {
    $_SESSION['error'] = 'You do not have permission to edit blotter entries.';
    header('Location: blotter.php');
    exit();
}

// Set page title
$page_title = 'Edit Blotter Entry';

// Check if blotter ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = 'No blotter entry specified to edit.';
    header('Location: blotter.php');
    exit();
}

$blotter_id = $_GET['id'];

// Fetch blotter entry details
try {
    $stmt = $conn->prepare("
        SELECT * 
        FROM blotter_entries 
        WHERE blotter_id = :blotter_id
    ");
    $stmt->bindParam(':blotter_id', $blotter_id);
    $stmt->execute();
    $blotter = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$blotter) {
        $_SESSION['error'] = 'Blotter entry not found.';
        header('Location: blotter.php');
        exit();
    }

    // Check if status allows for editing - Allow Admin and Secretary to edit all cases
    $restricted_statuses = array('Closed', 'Transferred', 'Dismissed');
    $allowed_user_types = array('Admin', 'Secretary');
    if (in_array($blotter['status'], $restricted_statuses) && !in_array($_SESSION['user_type'], $allowed_user_types)) {
        $_SESSION['error'] = 'Cannot edit a ' . $blotter['status'] . ' case. Please contact an administrator.';
        header('Location: blotter.php');
        exit();
    }
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    header('Location: blotter.php');
    exit();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate form inputs
    $errors = array();
    
    $reference_number = $_POST['reference_number'];
    $case_type = $_POST['case_type'];
    $status = $_POST['status'];
    $incident_date = $_POST['incident_date'];
    $incident_location = $_POST['incident_location'];
    $incident_details = $_POST['incident_details'];
    
    $suspect_name = $_POST['suspect_name'];
    $suspect_contact = $_POST['suspect_contact'];
    $suspect_address = $_POST['suspect_address'];
    
    $complainant_name = $_POST['complainant_name'];
    $complainant_contact = $_POST['complainant_contact'];
    $complainant_address = $_POST['complainant_address'];
    
    $arrest_date = $_POST['arrest_date'];
    $arresting_officer = $_POST['arresting_officer'];
    $detention_facility = $_POST['detention_facility'];
    
    // Basic validations
    if (empty($reference_number)) {
        $errors[] = 'Reference number is required.';
    }
    
    if (empty($case_type)) {
        $errors[] = 'Case type is required.';
    }
    
    if (empty($suspect_name)) {
        $errors[] = 'Suspect name is required.';
    }
    
    if (empty($complainant_name)) {
        $errors[] = 'Complainant name is required.';
    }
    
    if (empty($incident_date)) {
        $errors[] = 'Incident date is required.';
    }
    
    // If no errors, proceed with updating
    if (empty($errors)) {
        try {
            $sql = "UPDATE blotter_entries SET 
                   reference_number = :reference_number,
                   case_type = :case_type,
                   status = :status,
                   incident_date = :incident_date,
                   incident_location = :incident_location,
                   incident_details = :incident_details,
                   suspect_name = :suspect_name,
                   suspect_contact = :suspect_contact,
                   suspect_address = :suspect_address,
                   complainant_name = :complainant_name,
                   complainant_contact = :complainant_contact,
                   complainant_address = :complainant_address,
                   arrest_date = :arrest_date,
                   arresting_officer = :arresting_officer,
                   detention_facility = :detention_facility,
                   updated_at = NOW()
                   WHERE blotter_id = :blotter_id";
                   
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':reference_number', $reference_number);
            $stmt->bindParam(':case_type', $case_type);
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':incident_date', $incident_date);
            $stmt->bindParam(':incident_location', $incident_location);
            $stmt->bindParam(':incident_details', $incident_details);
            $stmt->bindParam(':suspect_name', $suspect_name);
            $stmt->bindParam(':suspect_contact', $suspect_contact);
            $stmt->bindParam(':suspect_address', $suspect_address);
            $stmt->bindParam(':complainant_name', $complainant_name);
            $stmt->bindParam(':complainant_contact', $complainant_contact);
            $stmt->bindParam(':complainant_address', $complainant_address);
            $stmt->bindParam(':arrest_date', $arrest_date);
            $stmt->bindParam(':arresting_officer', $arresting_officer);
            $stmt->bindParam(':detention_facility', $detention_facility);
            $stmt->bindParam(':blotter_id', $blotter_id);
            
            if ($stmt->execute()) {
                // Log the action
                $user_id = $_SESSION['user_id'];
                $action_details = "Updated blotter entry: " . $reference_number;
                $log_stmt = $conn->prepare("INSERT INTO activity_logs (user_id, action_type, action_details, module, action_timestamp) VALUES (:user_id, 'update', :action_details, 'Bail Management', NOW())");
                $log_stmt->bindParam(':user_id', $user_id);
                $log_stmt->bindParam(':action_details', $action_details);
                $log_stmt->execute();
                
                $_SESSION['success'] = 'Blotter entry updated successfully.';
                header('Location: view_case.php?id=' . $blotter_id);
                exit();
            } else {
                $_SESSION['error'] = 'Failed to update blotter entry.';
            }
        } catch (PDOException $e) {
            $_SESSION['error'] = 'Database error: ' . $e->getMessage();
        }
    } else {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// Include header and sidebar
include '../../includes/header.php';
include '../../includes/sidebar.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="mt-4">Edit Blotter Entry</h1>
            <ol class="breadcrumb mb-4">
                <li class="breadcrumb-item"><a href="../../index.php">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="index.php">Bail Management</a></li>
                <li class="breadcrumb-item"><a href="blotter.php">Blotter Entries</a></li>
                <li class="breadcrumb-item active">Edit Entry</li>
            </ol>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php 
                    echo $_SESSION['success']; 
                    unset($_SESSION['success']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php 
                    echo $_SESSION['error']; 
                    unset($_SESSION['error']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-1"></i>
                        Edit Blotter Details
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="edit_blotter.php?id=<?php echo $blotter_id; ?>">
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <strong>Case Information</strong>
                                    </div>
                                    <div class="card-body">
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <div class="form-floating mb-3">
                                                    <input type="text" class="form-control" id="reference_number" name="reference_number" placeholder="Reference Number" value="<?php echo htmlspecialchars($blotter['reference_number']); ?>" required>
                                                    <label for="reference_number">Reference Number</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating mb-3">
                                                    <input type="text" class="form-control" id="case_type" name="case_type" placeholder="Case Type" value="<?php echo htmlspecialchars($blotter['case_type']); ?>" required>
                                                    <label for="case_type">Case Type</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-4">
                                                <div class="form-floating mb-3">
                                                    <select class="form-select" id="status" name="status" required>
                                                        <option value="Active" <?php echo ($blotter['status'] == 'Active') ? 'selected' : ''; ?>>Active</option>
                                                        <option value="On Bail" <?php echo ($blotter['status'] == 'On Bail') ? 'selected' : ''; ?>>On Bail</option>
                                                        <option value="Closed" <?php echo ($blotter['status'] == 'Closed') ? 'selected' : ''; ?>>Closed</option>
                                                        <option value="Transferred" <?php echo ($blotter['status'] == 'Transferred') ? 'selected' : ''; ?>>Transferred</option>
                                                        <option value="Dismissed" <?php echo ($blotter['status'] == 'Dismissed') ? 'selected' : ''; ?>>Dismissed</option>
                                                    </select>
                                                    <label for="status">Status</label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-floating mb-3">
                                                    <input type="datetime-local" class="form-control" id="incident_date" name="incident_date" value="<?php echo date('Y-m-d\TH:i', strtotime($blotter['incident_date'])); ?>" required>
                                                    <label for="incident_date">Incident Date & Time</label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-floating mb-3">
                                                    <input type="text" class="form-control" id="incident_location" name="incident_location" placeholder="Incident Location" value="<?php echo htmlspecialchars($blotter['incident_location']); ?>" required>
                                                    <label for="incident_location">Incident Location</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-12">
                                                <div class="form-floating mb-3">
                                                    <textarea class="form-control" id="incident_details" name="incident_details" placeholder="Incident Details" style="height: 100px" required><?php echo htmlspecialchars($blotter['incident_details']); ?></textarea>
                                                    <label for="incident_details">Incident Details</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-danger text-white">
                                        <strong>Suspect Information</strong>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="suspect_name" name="suspect_name" placeholder="Suspect Name" value="<?php echo htmlspecialchars($blotter['suspect_name']); ?>" required>
                                            <label for="suspect_name">Suspect Name</label>
                                        </div>
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="suspect_contact" name="suspect_contact" placeholder="Suspect Contact" value="<?php echo htmlspecialchars($blotter['suspect_contact']); ?>">
                                            <label for="suspect_contact">Suspect Contact</label>
                                        </div>
                                        <div class="form-floating mb-3">
                                            <textarea class="form-control" id="suspect_address" name="suspect_address" placeholder="Suspect Address" style="height: 100px"><?php echo htmlspecialchars($blotter['suspect_address']); ?></textarea>
                                            <label for="suspect_address">Suspect Address</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-info text-white">
                                        <strong>Complainant Information</strong>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="complainant_name" name="complainant_name" placeholder="Complainant Name" value="<?php echo htmlspecialchars($blotter['complainant_name']); ?>" required>
                                            <label for="complainant_name">Complainant Name</label>
                                        </div>
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="complainant_contact" name="complainant_contact" placeholder="Complainant Contact" value="<?php echo htmlspecialchars($blotter['complainant_contact']); ?>">
                                            <label for="complainant_contact">Complainant Contact</label>
                                        </div>
                                        <div class="form-floating mb-3">
                                            <textarea class="form-control" id="complainant_address" name="complainant_address" placeholder="Complainant Address" style="height: 100px"><?php echo htmlspecialchars($blotter['complainant_address']); ?></textarea>
                                            <label for="complainant_address">Complainant Address</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-secondary text-white">
                                        <strong>Arrest Information</strong>
                                    </div>
                                    <div class="card-body">
                                        <div class="row mb-3">
                                            <div class="col-md-4">
                                                <div class="form-floating mb-3">
                                                    <input type="datetime-local" class="form-control" id="arrest_date" name="arrest_date" value="<?php echo date('Y-m-d\TH:i', strtotime($blotter['arrest_date'])); ?>" required>
                                                    <label for="arrest_date">Arrest Date & Time</label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-floating mb-3">
                                                    <input type="text" class="form-control" id="arresting_officer" name="arresting_officer" placeholder="Arresting Officer" value="<?php echo htmlspecialchars($blotter['arresting_officer']); ?>" required>
                                                    <label for="arresting_officer">Arresting Officer</label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-floating mb-3">
                                                    <input type="text" class="form-control" id="detention_facility" name="detention_facility" placeholder="Detention Facility" value="<?php echo htmlspecialchars($blotter['detention_facility']); ?>" required>
                                                    <label for="detention_facility">Detention Facility</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-end">
                            <a href="view_case.php?id=<?php echo $blotter_id; ?>" class="btn btn-secondary me-2">Cancel</a>
                            <button type="submit" class="btn btn-primary">Update Blotter Entry</button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../../includes/footer.php'; ?> 