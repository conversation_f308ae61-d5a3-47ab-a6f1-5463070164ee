<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/utility.php';
include '../../includes/functions/permission_functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission - allow access if user has any resolution permission or manage_complaints
if (!hasPermission('view_resolutions') && !hasPermission('manage_complaints') && !hasPermission('view_complaint')) {
    header("Location: ../../index.php?error=permission");
    exit;
}

// Initialize variables
$search = '';
$date_from = '';
$date_to = '';
$resolution_type = '';
$current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 10;
$offset = ($current_page - 1) * $per_page;

// Process search and filters
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    if (isset($_GET['search']) && !empty($_GET['search'])) {
        $search = sanitize($_GET['search']);
    }
    
    if (isset($_GET['date_from']) && !empty($_GET['date_from'])) {
        $date_from = sanitize($_GET['date_from']);
    }
    
    if (isset($_GET['date_to']) && !empty($_GET['date_to'])) {
        $date_to = sanitize($_GET['date_to']);
    }
    
    if (isset($_GET['resolution_type']) && !empty($_GET['resolution_type'])) {
        $resolution_type = sanitize($_GET['resolution_type']);
    }
}

// Build query conditions
$conditions = [];
$params = [];

// Always get only resolved complaints
$conditions[] = "c.status = 'Resolved'";

if (!empty($search)) {
    $conditions[] = "(c.complaint_type LIKE :search 
                     OR res.resolution_type LIKE :search 
                     OR r1.last_name LIKE :search 
                     OR r1.first_name LIKE :search 
                     OR r2.last_name LIKE :search 
                     OR r2.first_name LIKE :search 
                     OR c.complainant_name LIKE :search 
                     OR c.respondent_name LIKE :search)";
    $params[':search'] = "%$search%";
}

if (!empty($date_from)) {
    $conditions[] = "res.resolution_date >= :date_from";
    $params[':date_from'] = $date_from . " 00:00:00";
}

if (!empty($date_to)) {
    $conditions[] = "res.resolution_date <= :date_to";
    $params[':date_to'] = $date_to . " 23:59:59";
}

if (!empty($resolution_type)) {
    $conditions[] = "res.resolution_type = :resolution_type";
    $params[':resolution_type'] = $resolution_type;
}

$where_clause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";

// Get total count for pagination
try {
    $count_query = "SELECT COUNT(DISTINCT res.resolution_id) as total
                    FROM resolutions res
                    JOIN complaints c ON res.complaint_id = c.complaint_id
                    LEFT JOIN residents r1 ON c.complainant_id = r1.resident_id
                    LEFT JOIN residents r2 ON c.respondent_id = r2.resident_id
                    $where_clause";
    
    $count_stmt = $conn->prepare($count_query);
    foreach ($params as $key => $val) {
        $count_stmt->bindValue($key, $val);
    }
    $count_stmt->execute();
    $total_count = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
} catch (PDOException $e) {
    error_log("Error counting resolutions: " . $e->getMessage());
    $total_count = 0;
}

$total_pages = ceil($total_count / $per_page);

// Get resolutions with pagination
try {
    $query = "SELECT res.*, 
              c.complaint_type, c.complainant_id, c.respondent_id, c.complainant_name, c.respondent_name,
              r1.first_name as complainant_fname, r1.last_name as complainant_lname,
              r2.first_name as respondent_fname, r2.last_name as respondent_lname,
              u.username as resolved_by_name
              FROM resolutions res
              JOIN complaints c ON res.complaint_id = c.complaint_id
              LEFT JOIN residents r1 ON c.complainant_id = r1.resident_id
              LEFT JOIN residents r2 ON c.respondent_id = r2.resident_id
              LEFT JOIN users u ON res.resolved_by = u.user_id
              $where_clause
              ORDER BY res.resolution_date DESC
              LIMIT :offset, :per_page";
    
    $stmt = $conn->prepare($query);
    foreach ($params as $key => $val) {
        $stmt->bindValue($key, $val);
    }
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':per_page', $per_page, PDO::PARAM_INT);
    $stmt->execute();
    $resolutions = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching resolutions: " . $e->getMessage());
    $resolutions = [];
}

// Get counts for resolution types
try {
    // Get resolution type counts
    $type_counts = [
        'Amicable Settlement' => 0,
        'Mediation' => 0,
        'Dismissed' => 0,
        'Resolved' => 0
    ];
    $count_by_type_query = "SELECT resolution_type, COUNT(*) as count 
                           FROM resolutions 
                           GROUP BY resolution_type";
    $count_stmt = $conn->prepare($count_by_type_query);
    $count_stmt->execute();
    $type_counts_result = $count_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($type_counts_result as $count) {
        if (isset($type_counts[$count['resolution_type']])) {
            $type_counts[$count['resolution_type']] = $count['count'];
        }
    }
    
    // Combine 'Resolved' into 'Amicable Settlement' for display purposes
    if (isset($type_counts['Resolved'])) {
        $type_counts['Amicable Settlement'] += $type_counts['Resolved'];
    }
} catch (PDOException $e) {
    error_log("Error counting resolution types: " . $e->getMessage());
}

// Get unique resolution types for filter dropdown
try {
    $types_query = "SELECT DISTINCT resolution_type FROM resolutions ORDER BY resolution_type";
    $types_stmt = $conn->prepare($types_query);
    $types_stmt->execute();
    $resolution_types = $types_stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    error_log("Error fetching resolution types: " . $e->getMessage());
    $resolution_types = [];
}

// Generate pagination links
function generatePaginationLinks($current_page, $total_pages, $search, $date_from, $date_to, $resolution_type) {
    $links = '';
    $url_params = http_build_query([
        'search' => $search,
        'date_from' => $date_from,
        'date_to' => $date_to,
        'resolution_type' => $resolution_type
    ]);
    
    // Previous button
    if ($current_page > 1) {
        $links .= '<li class="page-item">
                     <a class="page-link" href="?page=' . ($current_page - 1) . '&' . $url_params . '" aria-label="Previous">
                       <span aria-hidden="true">&laquo;</span>
                     </a>
                   </li>';
    } else {
        $links .= '<li class="page-item disabled">
                     <a class="page-link" href="#" aria-label="Previous">
                       <span aria-hidden="true">&laquo;</span>
                     </a>
                   </li>';
    }
    
    // Page numbers
    $start_page = max(1, $current_page - 2);
    $end_page = min($total_pages, $current_page + 2);
    
    for ($i = $start_page; $i <= $end_page; $i++) {
        if ($i == $current_page) {
            $links .= '<li class="page-item active"><a class="page-link" href="#">' . $i . '</a></li>';
        } else {
            $links .= '<li class="page-item"><a class="page-link" href="?page=' . $i . '&' . $url_params . '">' . $i . '</a></li>';
        }
    }
    
    // Next button
    if ($current_page < $total_pages) {
        $links .= '<li class="page-item">
                     <a class="page-link" href="?page=' . ($current_page + 1) . '&' . $url_params . '" aria-label="Next">
                       <span aria-hidden="true">&raquo;</span>
                     </a>
                   </li>';
    } else {
        $links .= '<li class="page-item disabled">
                     <a class="page-link" href="#" aria-label="Next">
                       <span aria-hidden="true">&raquo;</span>
                     </a>
                   </li>';
    }
    
    return $links;
}

// Helper function to format resolution type
function getResolutionTypeBadge($type) {
    switch (strtolower($type)) {
        case 'amicable settlement':
            return '<span class="badge bg-success">🤝 Amicable Settlement</span>';
        case 'mediation':
            return '<span class="badge bg-primary">⚖️ Mediation</span>';
        case 'arbitration':
            return '<span class="badge bg-info">⚖️ Arbitration</span>';
        case 'conciliation':
            return '<span class="badge bg-primary">🤝 Conciliation</span>';
        case 'dismissed':
            return '<span class="badge bg-danger">❌ Dismissed</span>';
        case 'referred to higher authority':
            return '<span class="badge bg-warning">⬆️ Referred to Higher Authority</span>';
        case 'resolved':
            return '<span class="badge bg-success">✅ Resolved</span>';
        default:
            return '<span class="badge bg-secondary">' . $type . '</span>';
    }
}

// Page title
$page_title = "Resolutions - Barangay Management System";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resolutions Management - Barangay Management System</title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo get_favicon_url($conn, '../../'); ?>" type="image/png">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        body { 
            padding: 20px; 
            background-color: #f8f9fa;
        }
        .container-fluid { 
            max-width: 1800px;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1.5rem;
            border-radius: 0.5rem;
            overflow: hidden;
        }
        .card-header {
            font-weight: 600;
        }
        
        /* Left border colors */
        .border-left-primary { border-left: 4px solid #4e73df !important; }
        .border-left-success { border-left: 4px solid #1cc88a !important; }
        .border-left-info { border-left: 4px solid #36b9cc !important; }
        .border-left-warning { border-left: 4px solid #f6c23e !important; }
        .border-left-danger { border-left: 4px solid #e74a3b !important; }
        .border-left-secondary { border-left: 4px solid #858796 !important; }
        .border-left-purple { border-left: 4px solid #6f42c1 !important; }
        .border-left-orange { border-left: 4px solid #fd7e14 !important; }
        .border-left-teal { border-left: 4px solid #20c997 !important; }
        
        /* Text colors */
        .text-primary { color: #4e73df !important; }
        .text-success { color: #1cc88a !important; }
        .text-info { color: #36b9cc !important; }
        .text-warning { color: #f6c23e !important; }
        .text-danger { color: #e74a3b !important; }
        .text-purple { color: #6f42c1 !important; }
        .text-orange { color: #fd7e14 !important; }
        .text-teal { color: #20c997 !important; }
        
        /* Background colors with opacity */
        .bg-primary-soft { background-color: rgba(78, 115, 223, 0.1) !important; }
        .bg-success-soft { background-color: rgba(28, 200, 138, 0.1) !important; }
        .bg-info-soft { background-color: rgba(54, 185, 204, 0.1) !important; }
        .bg-warning-soft { background-color: rgba(246, 194, 62, 0.1) !important; }
        .bg-danger-soft { background-color: rgba(231, 74, 59, 0.1) !important; }
        .bg-purple-soft { background-color: rgba(111, 66, 193, 0.1) !important; }
        .bg-orange-soft { background-color: rgba(253, 126, 20, 0.1) !important; }
        .bg-teal-soft { background-color: rgba(32, 201, 151, 0.1) !important; }
        
        /* Button styles */
        .btn {
            border-radius: 0.35rem;
            padding: 0.375rem 1rem;
            transition: all 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
        }
        
        /* Badge styles */
        .badge {
            font-weight: 600;
            padding: 0.4em 0.8em;
            border-radius: 0.35rem;
        }
        
        /* Stats card */
        .stat-card {
            border-radius: 0.75rem;
            overflow: hidden;
            height: 100%;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            position: relative;
            z-index: 1;
        }
        .stat-card:hover {
            transform: translateY(-5px) !important;
            box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
            z-index: 2;
            cursor: pointer;
        }
        .stat-card:hover .stat-icon {
            transform: scale(1.1);
        }
        .stat-icon {
            font-size: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 60px;
            width: 60px;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }
        
        /* Border colors for different statuses */
        .border-primary { border-left: 4px solid #4e73df !important; }
        .border-success { border-left: 4px solid #1cc88a !important; }
        .border-info { border-left: 4px solid #36b9cc !important; }
        .border-warning { border-left: 4px solid #f6c23e !important; }
        .border-danger { border-left: 4px solid #e74a3b !important; }
        
        /* Table styles */
        .table {
            border-radius: 0.5rem;
            overflow: hidden;
        }
        .table th {
            background-color: #f8f9fc;
            border-bottom-width: 1px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.85rem;
            color: #4e73df;
        }
        
        /* Modal styles */
        .modal-content {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        /* Card hover effects */
        .card.shadow {
            transition: all 0.3s ease;
        }
        .card.shadow:hover {
            box-shadow: 0 0.5rem 1.5rem 0 rgba(58, 59, 69, 0.15) !important;
            transform: translateY(-3px);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>
    
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-0">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">📋 Case Resolutions</h1>
                    <?php if (hasPermission('manage_complaints') || hasPermission('view_complaint') || hasPermission('edit_complaint')): ?>
                    <div>
                        <a href="complaints.php" class="btn btn-primary">
                            <i class="fas fa-list"></i> Complaints List
                        </a>
                        <?php if (hasPermission('resolve_complaint') || hasPermission('edit_complaint')): ?>
                        <a href="complaints.php?status=Resolved" class="btn btn-success">
                            <i class="fas fa-check-circle"></i> Resolve Complaints
                        </a>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
                
                <!-- Summary Stats -->
                <div class="row">
                    <!-- Total Resolutions -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card stat-card border-primary">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-primary-soft text-primary">
                                            📊
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo $total_count; ?></h4>
                                        <p class="mb-0 text-muted">Total Resolutions</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Amicable Settlements -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card stat-card border-success">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-success-soft text-success">
                                            🤝
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo $type_counts['Amicable Settlement']; ?></h4>
                                        <p class="mb-0 text-muted">Resolved Amicably</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Mediation Cases -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card stat-card border-info">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-info-soft text-info">
                                            ⚖️
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo $type_counts['Mediation']; ?></h4>
                                        <p class="mb-0 text-muted">Mediation Cases</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Dismissed Cases -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card stat-card border-danger">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-danger-soft text-danger">
                                            ❌
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo $type_counts['Dismissed']; ?></h4>
                                        <p class="mb-0 text-muted">Dismissed Cases</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Search and Filters -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">🔍 Search & Filters</h6>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="resolutions.php" class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">🔍 Search</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" 
                                       placeholder="Search by name, type...">
                            </div>
                            <div class="col-md-2">
                                <label for="date_from" class="form-label">📅 From Date</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" 
                                       value="<?php echo $date_from; ?>">
                            </div>
                            <div class="col-md-2">
                                <label for="date_to" class="form-label">📅 To Date</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" 
                                       value="<?php echo $date_to; ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="resolution_type" class="form-label">📊 Resolution Type</label>
                                <select class="form-select" id="resolution_type" name="resolution_type">
                                    <option value="">All Types</option>
                                    <?php foreach ($resolution_types as $type): ?>
                                    <option value="<?php echo $type; ?>" <?php echo ($resolution_type == $type) ? 'selected' : ''; ?>>
                                        <?php echo $type; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <div class="d-grid gap-2 w-100">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Filter
                                    </button>
                                    <a href="resolutions.php" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> Clear
                                    </a>
                                </div>
                            </div>
                        </form>
            </div>
        </div>
        
                <!-- Resolutions Table -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-info">📝 Resolution List</h6>
                    </div>
                    <div class="card-body">
                        <?php if (count($resolutions) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover table-bordered-columns">
                                <thead class="table-light">
                                    <tr>
                                        <th>🔢 ID</th>
                                        <th>📄 Complaint</th>
                                        <th>👥 Parties</th>
                                        <th>🏷️ Resolution Type</th>
                                        <th>📅 Date</th>
                                        <th>👮‍♂️ Resolved By</th>
                                        <th class="action-column">⚙️ Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($resolutions as $resolution): ?>
                                    <tr>
                                        <td><?php echo $resolution['resolution_id']; ?></td>
                                        <td>
                                            <a href="view_complaint.php?id=<?php echo $resolution['complaint_id']; ?>" class="link-primary">
                                                #<?php echo $resolution['complaint_id']; ?> - <?php echo $resolution['complaint_type']; ?>
                                            </a>
                                        </td>
                                        <td>
                                            <?php 
                                            $complainant = !empty($resolution['complainant_id']) 
                                                ? $resolution['complainant_lname'] . ', ' . $resolution['complainant_fname']
                                                : $resolution['complainant_name'];
                                                
                                            $respondent = !empty($resolution['respondent_id'])
                                                ? $resolution['respondent_lname'] . ', ' . $resolution['respondent_fname']
                                                : $resolution['respondent_name'];
                                                
                                            echo $complainant . ' vs. ' . $respondent;
                                            ?>
                                        </td>
                                        <td><?php echo getResolutionTypeBadge($resolution['resolution_type']); ?></td>
                                        <td><?php echo date('M d, Y', strtotime($resolution['resolution_date'])); ?></td>
                                        <td><?php echo $resolution['resolved_by_name'] ?? ($resolution['resolved_by'] ?? 'System'); ?></td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="#" class="btn btn-sm btn-info view-resolution" 
                                                   data-id="<?php echo $resolution['resolution_id']; ?>" data-bs-toggle="tooltip" title="View Resolution">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                
                                                <?php if (hasPermission('print_resolution') || hasPermission('view_resolutions') || hasPermission('manage_complaints')): ?>
                                                <a href="print_resolution.php?id=<?php echo $resolution['resolution_id']; ?>"
                                                   class="btn btn-sm btn-primary" target="_blank" data-bs-toggle="tooltip" title="Print Resolution">
                                                    <i class="fas fa-print"></i>
                                                </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                <?php echo generatePaginationLinks($current_page, $total_pages, $search, $date_from, $date_to, $resolution_type); ?>
                            </ul>
                        </nav>
                        <?php endif; ?>
                        
                        <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> 📢 No resolutions found based on your search criteria.
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
            </div>
    </div>
    
    <!-- Reusable Resolution Modal -->
    <div class="modal fade" id="resolutionModal" tabindex="-1" aria-labelledby="resolutionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title fw-bold" id="resolutionModalLabel">
                        <i class="fas fa-file-contract me-2"></i> Resolution #<span id="resolution_id_display"></span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="resolution-loading" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading resolution details...</p>
                    </div>
                    <div id="resolution-content" class="d-none">
                        <!-- Resolution Summary -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card border-info h-100">
                                    <div class="card-header bg-info bg-opacity-10">
                                        <h6 class="card-title mb-0 fw-bold">
                                            <i class="fas fa-gavel me-2 text-info"></i> Complaint Information
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="mb-2">
                                            <i class="fas fa-hashtag me-2 text-info"></i>
                                            <span class="fw-bold">Complaint ID:</span> 
                                            <a href="#" id="complaint_link" class="text-decoration-none">
                                                #<span id="complaint_id_display"></span>
                                            </a>
                                        </p>
                                        <p class="mb-2">
                                            <i class="fas fa-folder me-2 text-info"></i>
                                            <span class="fw-bold">Type:</span> 
                                            <span id="complaint_type_display"></span>
                                        </p>
                                        <p class="mb-2">
                                            <i class="fas fa-user me-2 text-info"></i>
                                            <span class="fw-bold">Complainant:</span> 
                                            <span id="complainant_display"></span>
                                        </p>
                                        <p class="mb-2">
                                            <i class="fas fa-user me-2 text-info"></i>
                                            <span class="fw-bold">Respondent:</span> 
                                            <span id="respondent_display"></span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-success h-100">
                                    <div class="card-header bg-success bg-opacity-10">
                                        <h6 class="card-title mb-0 fw-bold">
                                            <i class="fas fa-check-circle me-2 text-success"></i> Resolution Details
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="mb-2">
                                            <i class="fas fa-tag me-2 text-success"></i>
                                            <span class="fw-bold">Resolution Type:</span> 
                                            <span id="resolution_type_badge"></span>
                                        </p>
                                        <p class="mb-2">
                                            <i class="fas fa-calendar-alt me-2 text-success"></i>
                                            <span class="fw-bold">Resolution Date:</span> 
                                            <span id="resolution_date_display"></span>
                                        </p>
                                        <p class="mb-2">
                                            <i class="fas fa-user-tie me-2 text-success"></i>
                                            <span class="fw-bold">Resolved By:</span> 
                                            <span id="resolved_by_display"></span>
                                        </p>
                                        <p class="mb-2 created-at-container d-none">
                                            <i class="fas fa-clock me-2 text-success"></i>
                                            <span class="fw-bold">Created:</span> 
                                            <span id="created_at_display"></span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Resolution Content -->
                        <div class="card border-primary mb-3">
                            <div class="card-header bg-primary bg-opacity-10">
                                <h6 class="card-title mb-0 fw-bold">
                                    <i class="fas fa-file-alt me-2 text-primary"></i> Resolution Content
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="p-3 bg-light rounded" id="resolution_content_display"></div>
                            </div>
                        </div>
                        
                        <!-- Agreement Terms (if available) -->
                        <div id="agreement_terms_container" class="card border-warning d-none">
                            <div class="card-header bg-warning bg-opacity-10">
                                <h6 class="card-title mb-0 fw-bold">
                                    <i class="fas fa-handshake me-2 text-warning"></i> Agreement Terms
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="p-3 bg-light rounded" id="agreement_terms_display"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="#" id="print_resolution_link" class="btn btn-primary" target="_blank"
                       <?php echo !(hasPermission('print_resolution') || hasPermission('view_resolutions') || hasPermission('manage_complaints')) ? 'd-none' : ''; ?>>
                        <i class="fas fa-print me-1"></i> Print Resolution
                    </a>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i> Close
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    <script>
        $(document).ready(function() {
            // View Resolution
            $(document).on('click', '.view-resolution', function(e) {
                e.preventDefault();
                const resolutionId = $(this).data('id');
                
                // Reset modal content
                $('#resolution_id_display').text(resolutionId);
                $('#resolution-content').addClass('d-none');
                $('#resolution-loading').removeClass('d-none');
                $('#agreement_terms_container').addClass('d-none');
                $('.created-at-container').addClass('d-none');
                
                // Update print link
                $('#print_resolution_link').attr('href', 'print_resolution.php?id=' + resolutionId);
                
                // Show modal
                $('#resolutionModal').modal('show');
                
                // Fetch resolution details via AJAX
                $.ajax({
                    url: 'get_resolution.php?id=' + resolutionId,
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        // Hide loading, show content
                        $('#resolution-loading').addClass('d-none');
                        $('#resolution-content').removeClass('d-none');
                        
                        if (data.error) {
                            $('#resolution-loading').removeClass('d-none')
                                .html('<div class="alert alert-danger">' + data.error + '</div>');
                            return;
                        }
                        
                        // Populate modal with resolution data
                        $('#complaint_id_display').text(data.complaint_id || '');
                        $('#complaint_link').attr('href', 'view_complaint.php?id=' + data.complaint_id);
                        $('#complaint_type_display').text(data.complaint_type || '');
                        $('#complainant_display').text(data.complainant_full_name || '');
                        $('#respondent_display').text(data.respondent_full_name || '');
                        
                        // Set resolution type badge
                        let badgeClass = 'bg-secondary';
                        let resolutionType = data.resolution_type || 'Unknown';
                        
                        if (resolutionType === 'Amicable Settlement') badgeClass = 'bg-success';
                        else if (resolutionType === 'Mediation') badgeClass = 'bg-info';
                        else if (resolutionType === 'Arbitration') badgeClass = 'bg-primary';
                        else if (resolutionType === 'Dismissed') badgeClass = 'bg-danger';
                        
                        $('#resolution_type_badge').html('<span class="badge ' + badgeClass + '">' + resolutionType + '</span>');
                        $('#resolution_date_display').text(data.formatted_resolution_date || '');
                        $('#resolved_by_display').text(data.resolved_by_name || data.resolved_by || 'System');
                        
                        // Show created_at if available
                        if (data.formatted_created_at) {
                            $('#created_at_display').text(data.formatted_created_at);
                            $('.created-at-container').removeClass('d-none');
                        }
                        
                        // Resolution content
                        if (data.resolution_content) {
                            $('#resolution_content_display').html(data.resolution_content.replace(/\n/g, '<br>'));
                        } else {
                            $('#resolution_content_display').html('<em>No content available</em>');
                        }
                        
                        // Agreement terms if available
                        if (data.agreement_terms) {
                            $('#agreement_terms_display').html(data.agreement_terms.replace(/\n/g, '<br>'));
                            $('#agreement_terms_container').removeClass('d-none');
                        }
                    },
                    error: function(xhr, status, error) {
                        $('#resolution-loading').removeClass('d-none')
                            .html('<div class="alert alert-danger">Error loading resolution details: ' + error + '. Please try again.</div>');
                    }
                });
            });
        });
        
        // Function to show toast notifications
        function showToast(message, type = 'info') {
            // Create toast element
            const toastId = 'toast-' + Date.now();
            const toast = `
                <div class="toast-container position-fixed bottom-0 end-0 p-3">
                    <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                        <div class="d-flex">
                            <div class="toast-body">
                                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                                ${message}
                            </div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                    </div>
                </div>
            `;
            
            // Append toast to body
            document.body.insertAdjacentHTML('beforeend', toast);
            
            // Initialize and show toast
            const toastElement = document.getElementById(toastId);
            const bsToast = new bootstrap.Toast(toastElement, {
                autohide: true,
                delay: 5000
            });
            bsToast.show();
        }
        
        // Show toast messages for session alerts
        <?php 
        if (isset($_SESSION['message'])) {
            $type = isset($_SESSION['message_type']) ? $_SESSION['message_type'] : 'info';
            echo "showToast('" . addslashes($_SESSION['message']) . "', '$type');";
            unset($_SESSION['message']);
            unset($_SESSION['message_type']);
        }
        ?>
    </script>
</body>
</html>