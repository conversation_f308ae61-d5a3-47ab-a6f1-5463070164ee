<?php
session_start();
include 'includes/config/database.php';
include 'includes/functions/permission_functions.php';
include 'includes/functions/utility.php';
include 'includes/functions/functions.php';

echo "<h2>🧪 Test Print Resolution Functionality</h2>";

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "❌ Please login first<br>";
    echo "<a href='login.php'>Go to Login</a>";
    exit;
}

echo "✅ User logged in: " . $_SESSION['user_type'] . "<br>";

// Check permission
if (!hasPermission('print_resolution') && !hasPermission('view_resolutions') && !hasPermission('manage_complaints')) {
    echo "❌ No permission to print resolutions<br>";
    exit;
}

echo "✅ Permission check passed<br>";

// Test logActivity function
echo "<h3>Testing logActivity function:</h3>";
try {
    if (function_exists('logActivity')) {
        echo "✅ logActivity function exists<br>";
        
        // Test the function call
        $result = logActivity($conn, 'Test Print', $_SESSION['user_id'], 'complaints', 'Testing print resolution functionality');
        echo "✅ logActivity call successful: " . ($result ? 'true' : 'false') . "<br>";
    } else {
        echo "❌ logActivity function does not exist<br>";
    }
} catch (Exception $e) {
    echo "❌ Error calling logActivity: " . $e->getMessage() . "<br>";
}

// Test database connection
echo "<h3>Testing database connection:</h3>";
try {
    $test_query = $conn->query("SELECT COUNT(*) FROM resolutions LIMIT 1");
    echo "✅ Database connection working<br>";
    echo "✅ Resolutions table accessible<br>";
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

echo "<br><a href='modules/complaints/resolutions.php'>Back to Resolutions</a><br>";
echo "<a href='modules/complaints/print_resolution.php?id=1'>Test Print Resolution (ID=1)</a><br>";
?>
