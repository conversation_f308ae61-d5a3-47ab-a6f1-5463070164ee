<?php
// Minimal test for print resolution functionality
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
include 'includes/config/database.php';
include 'includes/functions/permission_functions.php';
include 'includes/functions/utility.php';
include 'includes/functions/functions.php';

echo "<h2>🧪 Minimal Print Resolution Test</h2>";

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "❌ Please login first<br>";
    exit;
}

// Check permission
if (!hasPermission('print_resolution') && !hasPermission('view_resolutions') && !hasPermission('manage_complaints')) {
    echo "❌ No permission<br>";
    exit;
}

$resolution_id = isset($_GET['id']) ? (int)$_GET['id'] : 1;

try {
    // Get resolution data
    $query = "SELECT r.*, c.complaint_type, c.complainant_name, c.respondent_name 
              FROM resolutions r
              LEFT JOIN complaints c ON r.complaint_id = c.complaint_id
              WHERE r.resolution_id = ?";
    
    $stmt = $conn->prepare($query);
    $stmt->execute([$resolution_id]);
    $resolution = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$resolution) {
        echo "❌ Resolution not found<br>";
        exit;
    }
    
    echo "✅ Resolution found: " . $resolution['resolution_type'] . "<br>";
    
    // Test the logActivity call that was causing issues
    echo "Testing logActivity call...<br>";
    
    if (function_exists('logActivity')) {
        $result = logActivity($conn, 'Print Resolution', $_SESSION['user_id'], 'complaints', "Printed resolution #$resolution_id for complaint #{$resolution['complaint_id']}");
        echo "✅ logActivity successful: " . ($result ? 'true' : 'false') . "<br>";
    } else {
        echo "❌ logActivity function not found<br>";
    }
    
    echo "<br>✅ All tests passed! Print resolution should work.<br>";
    echo "<a href='modules/complaints/print_resolution.php?id=$resolution_id' target='_blank'>🖨️ Test Full Print Resolution</a><br>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . "<br>";
    echo "❌ Line: " . $e->getLine() . "<br>";
}
?>
