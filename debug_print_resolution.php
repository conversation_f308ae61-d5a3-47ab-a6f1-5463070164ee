<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
include 'includes/config/database.php';
include 'includes/functions/permission_functions.php';
include 'includes/functions/utility.php';
include 'includes/functions/functions.php';

echo "<h2>🔍 Debug Print Resolution</h2>";

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "❌ Please login first<br>";
    exit;
}

echo "✅ User: " . $_SESSION['user_type'] . " (ID: " . $_SESSION['user_id'] . ")<br>";

// Check if resolution ID is provided
$resolution_id = isset($_GET['id']) ? (int)$_GET['id'] : 1;
echo "📋 Testing with Resolution ID: $resolution_id<br>";

// Check permission
echo "<h3>Permission Check:</h3>";
$has_print = hasPermission('print_resolution');
$has_view = hasPermission('view_resolutions');
$has_manage = hasPermission('manage_complaints');

echo "print_resolution: " . ($has_print ? '✅' : '❌') . "<br>";
echo "view_resolutions: " . ($has_view ? '✅' : '❌') . "<br>";
echo "manage_complaints: " . ($has_manage ? '✅' : '❌') . "<br>";

if (!$has_print && !$has_view && !$has_manage) {
    echo "❌ No permission to access print resolution<br>";
    exit;
}

// Test database query
echo "<h3>Database Query Test:</h3>";
try {
    $query = "SELECT r.*, c.complaint_type, c.complainant_name, c.respondent_name, c.incident_date, c.incident_location
              FROM resolutions r
              LEFT JOIN complaints c ON r.complaint_id = c.complaint_id
              WHERE r.resolution_id = ?";
    
    $stmt = $conn->prepare($query);
    $stmt->execute([$resolution_id]);
    $resolution = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($resolution) {
        echo "✅ Resolution found: " . $resolution['resolution_type'] . "<br>";
        echo "✅ Complaint ID: " . $resolution['complaint_id'] . "<br>";
    } else {
        echo "❌ No resolution found with ID: $resolution_id<br>";
        exit;
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
    exit;
}

// Test logActivity function
echo "<h3>LogActivity Function Test:</h3>";
try {
    echo "Function exists: " . (function_exists('logActivity') ? '✅' : '❌') . "<br>";
    
    if (function_exists('logActivity')) {
        echo "Attempting to call logActivity...<br>";
        $result = logActivity($conn, 'Debug Print Test', $_SESSION['user_id'], 'complaints', "Debug test for resolution #$resolution_id");
        echo "✅ logActivity successful: " . ($result ? 'true' : 'false') . "<br>";
    }
} catch (Exception $e) {
    echo "❌ logActivity error: " . $e->getMessage() . "<br>";
    echo "❌ Error on line: " . $e->getLine() . "<br>";
    echo "❌ Error file: " . $e->getFile() . "<br>";
}

echo "<br><h3>🎯 Test Results:</h3>";
echo "If no errors appeared above, the print resolution should work.<br>";
echo "<a href='modules/complaints/print_resolution.php?id=$resolution_id' target='_blank'>🖨️ Test Print Resolution</a><br>";
echo "<a href='modules/complaints/resolutions.php'>📋 Back to Resolutions</a><br>";
?>
