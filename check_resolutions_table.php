<?php
session_start();
include 'includes/config/database.php';

echo "<h2>🔍 Check Resolutions Table Structure</h2>";

try {
    // Check resolutions table structure
    $query = "DESCRIBE resolutions";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>📋 Resolutions Table Columns:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check if resolution_content column exists
    $has_content = false;
    $content_alternatives = [];
    
    foreach ($columns as $column) {
        if ($column['Field'] == 'resolution_content') {
            $has_content = true;
        }
        if (strpos($column['Field'], 'content') !== false || 
            strpos($column['Field'], 'details') !== false || 
            strpos($column['Field'], 'description') !== false ||
            strpos($column['Field'], 'text') !== false) {
            $content_alternatives[] = $column['Field'];
        }
    }
    
    echo "<h3>🎯 Analysis:</h3>";
    if ($has_content) {
        echo "✅ resolution_content column exists<br>";
    } else {
        echo "❌ resolution_content column does NOT exist<br>";
        if (!empty($content_alternatives)) {
            echo "🔍 Possible alternatives found: " . implode(', ', $content_alternatives) . "<br>";
        }
    }
    
    // Get sample data from resolutions table
    echo "<h3>📊 Sample Resolution Data:</h3>";
    $sample_query = "SELECT * FROM resolutions LIMIT 1";
    $sample_stmt = $conn->prepare($sample_query);
    $sample_stmt->execute();
    $sample = $sample_stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($sample) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Column</th><th>Value</th></tr>";
        foreach ($sample as $key => $value) {
            echo "<tr>";
            echo "<td><strong>" . $key . "</strong></td>";
            echo "<td>" . (strlen($value) > 100 ? substr($value, 0, 100) . "..." : $value) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ No sample data found in resolutions table<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}
?>
